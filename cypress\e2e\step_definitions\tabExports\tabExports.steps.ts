import {
  When,
  Before,
  Given,
  Then,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  landingPage.loginLandingPage();
  mediaListPage.goToMediaListPage();
});

Given('The user navigates to the tab {string}', (tabName: string) => {
  cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
});

Then(
  'The {string} tab should display the total number of files',
  (tabName: string) => {
    cy.get('[data-test="history-tab-button"]')
      .contains(new RegExp(`^${tabName}\\(\\d+\\)$`))
      .should('be.visible');
  }
);

Then(
  'The exports table should have the following columns:',
  (dataTable: DataTable) => {
    dataTable.raw().forEach((row) => {
      const columnName = row[0];
      if (columnName) {
        cy.get('[data-testid="export-table-row"]')
          .closest('table')
          .find('thead th')
          .contains(columnName)
          .should('be.visible');
      }
    });
  }
);

Then('The table should have pagination controls', () => {
  cy.get('div[role="combobox"]').should('be.visible');
  cy.contains(/\d+–\d+ of \d+/).should('be.visible');
  cy.get('button[aria-label="Previous Page"]').should('be.visible');
  cy.get('button[aria-label="Next Page"]').should('be.visible');
});

Then(
  'Each export file name in the list should follow the default format',
  () => {
    const nameFormatRegex = /^export_\d{8}_\d{2}_\d{2}_\d{2}$/;
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row)
        .find('td')
        .eq(1)
        .invoke('text')
        .should('match', nameFormatRegex);
    });
  }
);

Then(
  'Each export file in the list should have a {string} status',
  (expectedStatus: string) => {
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row).find('td').eq(2).should('have.text', expectedStatus);
    });
  }
);

Then(
  'The date initiated for each export file should follow the correct format',
  () => {
    const dateFormatRegex = /^\w{3}\s\w{3}\s\d{1,2}\s\d{4}\s\d{2}:\d{2}:\d{2}$/;
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row)
        .find('td')
        .eq(3)
        .invoke('text')
        .should('match', dateFormatRegex);
    });
  }
);

When('The user clicks the download button for an export file', () => {
  cy.get('[data-testid="export-table-row"]')
    .first()
    .find('button[data-name]')
    .click();
});

Given(
  'The rows per page selector defaults to {string}',
  (defaultRows: string) => {
    cy.get('div[role="combobox"]').should('have.text', defaultRows);
  }
);

Then(
  'The user verifies changing the rows per page for the following values:',
  (dataTable: DataTable) => {
    cy.contains(/\d+–\d+ of \d+/)
      .invoke('text')
      .then((initialText) => {
        const totalResults = parseInt(initialText.split(' of ')[1], 10);

        dataTable.raw().forEach((row) => {
          const rowsPerPage = row[0] ?? '0';
          const expectedRowCount = parseInt(rowsPerPage, 10);
          const expectedVisibleRows = Math.min(expectedRowCount, totalResults);

          cy.get('div[role="combobox"]').click();
          cy.get(`li[role="option"][data-value="${rowsPerPage}"]`).click();

          const expectedText = `1–${expectedVisibleRows} of ${totalResults}`;
          cy.contains(expectedText).should('be.visible');
        });
        return null;
      });
  }
);

Then(
  'The pagination controls for the {string} page are correct',
  (page: string) => {
    if (page === 'first') {
      cy.contains(/1–\d+ of \d+/).should('be.visible');
      cy.get('button[aria-label="Previous Page"]').should('be.disabled');
      cy.get('button[aria-label="Next Page"]').should('be.enabled');
    } else if (page === 'second') {
      cy.contains(/11–\d+ of \d+/).should('be.visible');
      cy.get('button[aria-label="Previous Page"]').should('be.enabled');
      cy.get('button[aria-label="Next Page"]').should('be.enabled');
    }
  }
);

When('The user clicks the {string} page button', (direction: string) => {
  cy.get(`button[aria-label="${direction} Page"]`).click();
});
