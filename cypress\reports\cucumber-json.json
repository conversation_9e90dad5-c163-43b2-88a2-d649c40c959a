[{"description": "", "elements": [{"description": "", "id": "exportlocal;verify-user-can-create-your-own-export-template", "keyword": "<PERSON><PERSON><PERSON>", "line": 52, "name": "Verify user can create your own export template", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 19389000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on the media list page", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:14"}, "result": {"status": "passed", "duration": 7874000000}}, {"arguments": [], "keyword": "Given ", "line": 53, "name": "The user deletes the export template \"My New Test Template\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:214"}, "result": {"status": "passed", "duration": 4307000000}}, {"arguments": [], "keyword": "Given ", "line": 54, "name": "The user is on the media list page", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:14"}, "result": {"status": "passed", "duration": 19197000000}}, {"arguments": [], "keyword": "When ", "line": 55, "name": "The user selects the file \"e2e_audio.mp3\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:26"}, "result": {"status": "passed", "duration": 715000000}}, {"arguments": [], "keyword": "And ", "line": 56, "name": "The user clicks the \"Export\" toolbar button", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:33"}, "result": {"status": "passed", "duration": 862000000}}, {"arguments": [], "keyword": "Then ", "line": 57, "name": "The \"Advanced Export\" pop-up is displayed", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:51"}, "result": {"status": "passed", "duration": 254000000}}, {"arguments": [], "keyword": "When ", "line": 58, "name": "The user checks the \"Filename\" export option", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:112"}, "result": {"status": "passed", "duration": 341000000}}, {"arguments": [], "keyword": "And ", "line": 59, "name": "The user clicks the \"Save Template\" button on the export pop-up", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:181"}, "result": {"status": "passed", "duration": 471000000}}, {"arguments": [], "keyword": "Then ", "line": 60, "name": "The \"Name Template\" dialog should appear", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:189"}, "result": {"status": "passed", "duration": 109000000}}, {"arguments": [], "keyword": "When ", "line": 61, "name": "The user names the template \"My New Test Template\"", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:193"}, "result": {"status": "passed", "duration": 1582000000}}, {"arguments": [], "keyword": "And ", "line": 62, "name": "The user clicks the \"Save template\" button in the new template dialog", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:201"}, "result": {"status": "passed", "duration": 488000000}}, {"arguments": [], "keyword": "Then ", "line": 63, "name": "A success message \"Template saved\" is displayed", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/exportLocal/exportLocal.spec.ts:209"}, "result": {"status": "passed", "duration": 1522000000}}], "tags": [{"name": "@e2e", "line": 51}, {"name": "@exportLocal", "line": 51}], "type": "scenario"}], "id": "exportlocal", "line": 1, "keyword": "Feature", "name": "ExportLocal", "tags": [], "uri": "cypress\\e2e\\features\\exportLocal.feature"}]