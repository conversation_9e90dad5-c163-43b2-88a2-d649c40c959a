export const exportPage = {
  // File selection methods
  selectFile: (fileName: string): void => {
    cy.contains('[data-testid^="files-table-row-"]', fileName)
      .first()
      .find('input[type="checkbox"]')
      .check();
  },

  clickToolbarButton: (buttonName: string): void => {
    const buttonSelectors: { [key: string]: string } = {
      tag: '[data-test="files-bulk-tag-icon-button"]',
      export: '[data-test="files-bulk-export-icon-button"]',
      'run process': '[data-test="reprocess-file-icon-button"]',
      move: '[data-test="files-bulk-move-icon-button"]',
      delete: '[data-test="files-bulk-delete-icon-button"]',
      'send to redact': '[data-test="files-send-to-redact-icon-button"]',
    };

    const selector = buttonSelectors[buttonName.toLowerCase()];
    if (!selector) {
      throw new Error(`Button "${buttonName}" is not defined`);
    }

    cy.get(selector).click();
  },

  // Export popup verification methods
  verifyAdvancedExportPopupDisplayed: (): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .should('be.visible')
      .within(() => {
        cy.get('h1').contains('advanced export', { matchCase: false });
      });
  },

  verifyTabSelected: (tabName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find('button[role="tab"]')
      .contains(tabName)
      .should('have.attr', 'aria-selected', 'true');
  },

  verifyExportOptionsVisible: (options: string[]): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      options.forEach((optionName: string) => {
        cy.contains('p', optionName).should('be.visible');
      });
    });
  },

  verifyButtonsVisibleOnExportPopup: (buttons: string[]): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      buttons.forEach((buttonName: string) => {
        cy.contains('button', buttonName).should('be.visible');
      });
    });
  },

  // Export option interaction methods
  verifyExportOptionChecked: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('be.checked');
  },

  verifyExportOptionDisabled: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('be.disabled');
  },

  checkExportOption: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .check({ force: true });
  },

  uncheckExportOption: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .uncheck({ force: true });
  },

  verifyExportOptionNotChecked: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('not.be.checked');
  },

  checkMultipleExportOptions: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .check({ force: true });
    });
  },

  uncheckMultipleExportOptions: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .uncheck({ force: true });
    });
  },

  verifyMultipleExportOptionsChecked: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .should('be.checked');
    });
  },

  // Field editing methods
  clickEditIconForField: (fieldName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find(`label:has(p:contains("${fieldName}")) + [data-testid="EditIcon"]`)
      .click();
  },

  renameField: (originalName: string, newName: string): void => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .clear();

      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .type(newName);
    });
  },

  clickDialogButton: (buttonName: string): void => {
    cy.get('[role="dialog"]').contains('button', buttonName).click();
  },

  verifyInputValue: (labelName: string, expectedValue: string): void => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.get(`div:has(> label:contains("${labelName}"))`)
        .find('input[type="text"]')
        .should('have.value', expectedValue);
    });
  },

  // Export popup button interactions
  clickExportPopupButton: (buttonName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  },

  // Dialog verification and interaction
  verifyDialogAppears: (name: string): void => {
    cy.contains('[role="dialog"]', name).should('be.visible');
  },

  nameTemplate: (templateName: string): void => {
    cy.contains('[role="dialog"]', 'Name Template').within(() => {
      cy.get('#export-template-name').clear();
      cy.get('#export-template-name').type(templateName);
    });
  },

  clickNewTemplateDialogButton: (buttonName: string): void => {
    cy.contains('[role="dialog"]', 'Name Template')
      .contains('button', buttonName)
      .click();
  },

  verifySuccessMessage: (message: string): void => {
    cy.get('[role="alert"]').should('be.visible').and('contain', message);
  },

  // Template selection
  selectExportTemplate: (templateName: string): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      cy.get('#export-template-selected').click();
    });
    cy.get('[role="option"]').contains(templateName).click();
  },

  // Tab navigation
  navigateToTab: (tabName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find('button[role="tab"]')
      .contains(tabName)
      .click();
  },

  // Archive name field methods
  clearArchiveNameField: (): void => {
    cy.get('[data-testid="export-file-name"]').find('input').clear();
  },

  verifyArchiveNameErrorMessage: (errorMessage: string): void => {
    cy.get('#export-file-name-helper-text')
      .should('be.visible')
      .and('contain', errorMessage);
  },

  verifyExportButtonDisabled: (): void => {
    cy.get('[data-test="export-select-button"]').should('be.disabled');
  },

  setArchiveName: (archiveName: string): void => {
    cy.get('[data-testid="export-file-name"]').find('input').type(archiveName);
  },

  verifyArchiveNameNoErrorMessage: (): void => {
    cy.get('#export-file-name-helper-text').should('not.have.class', 'Mui-error');
  },

  verifyExportButtonEnabled: (): void => {
    cy.get('[data-test="export-select-button"]').should('be.enabled');
  },

  // Option tab button interactions
  clickOptionTabButton: (buttonName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  },

  // Password dialog methods
  verifySetPasswordDialogAppears: (): void => {
    cy.get('div:has(#encryption-password)').should('be.visible');
  },

  enterPassword: (password: string): void => {
    cy.get('#encryption-password').type(password);
  },

  clickPasswordDialogButton: (buttonName: string): void => {
    cy.get('div:has(#encryption-password)')
      .contains('button', buttonName)
      .click();
  },

  verifySetPasswordDialogDisappears: (): void => {
    cy.get('#encryption-password').should('not.exist');
  },

  // Notifications panel methods
  openNotificationsPanel: (): void => {
    cy.get('#notification-image').click();
  },

  downloadFirstCompletedExportFromNotifications: (): void => {
    cy.contains('li', 'complete', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .click();
  },

  closeNotificationsPanel: (): void => {
    cy.get('body').click(0, 0);
  },

  // Main application tab navigation
  navigateToMainApplicationTab: (tabName: string): void => {
    cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
  },

  // Export table verification
  verifyCanDownloadFirstCompletedExport: (): void => {
    cy.get('tbody tr:has(span:contains("complete"))', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .should('be.enabled');
  },
};
