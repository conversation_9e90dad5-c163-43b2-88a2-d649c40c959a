export const exportPage = {
  selectFile: (fileName: string): void => {
    cy.contains('[data-testid^="files-table-row-"]', fileName)
      .first()
      .find('input[type="checkbox"]')
      .check();
  },

  clickToolbarButton: (buttonName: string): void => {
    const buttonSelectors: { [key: string]: string } = {
      tag: '[data-test="files-bulk-tag-icon-button"]',
      export: '[data-test="files-bulk-export-icon-button"]',
      'run process': '[data-test="reprocess-file-icon-button"]',
      move: '[data-test="files-bulk-move-icon-button"]',
      delete: '[data-test="files-bulk-delete-icon-button"]',
      'send to redact': '[data-test="files-send-to-redact-icon-button"]',
    };

    const selector = buttonSelectors[buttonName.toLowerCase()];
    if (!selector) {
      throw new Error(`Button "${buttonName}" is not defined`);
    }

    cy.get(selector).click();
  },

  verifyAdvancedExportPopupDisplayed: (): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .should('be.visible')
      .within(() => {
        cy.get('h1').contains('advanced export', { matchCase: false });
      });
  },

  verifyTabSelected: (tabName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find('button[role="tab"]')
      .contains(tabName)
      .should('have.attr', 'aria-selected', 'true');
  },

  verifyExportOptionsVisible: (options: string[]): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      options.forEach((optionName: string) => {
        cy.contains('p', optionName).should('be.visible');
      });
    });
  },

  verifyButtonsVisibleOnExportPopup: (buttons: string[]): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      buttons.forEach((buttonName: string) => {
        cy.contains('button', buttonName).should('be.visible');
      });
    });
  },

  verifyExportOptionChecked: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('be.checked');
  },

  verifyExportOptionDisabled: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('be.disabled');
  },

  checkExportOption: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .check({ force: true });
  },

  uncheckExportOption: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .uncheck({ force: true });
  },

  verifyExportOptionNotChecked: (optionName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('label', optionName)
      .find('input[type="checkbox"]')
      .should('not.be.checked');
  },

  checkMultipleExportOptions: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .check({ force: true });
    });
  },

  uncheckMultipleExportOptions: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .uncheck({ force: true });
    });
  },

  verifyMultipleExportOptionsChecked: (options: string[]): void => {
    options.forEach((optionName: string) => {
      cy.get('[data-test="panel-bulk-export"]')
        .contains('label', optionName)
        .find('input[type="checkbox"]')
        .should('be.checked');
    });
  },

  clickEditIconForField: (fieldName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find(`label:has(p:contains("${fieldName}")) + [data-testid="EditIcon"]`)
      .click();
  },

  renameField: (originalName: string, newName: string): void => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .clear();

      cy.contains('label', originalName)
        .parent()
        .find('input[type="text"]')
        .type(newName);
    });
  },

  clickDialogButton: (buttonName: string): void => {
    cy.get('[role="dialog"]').contains('button', buttonName).click();
  },

  verifyInputValue: (labelName: string, expectedValue: string): void => {
    cy.contains('[role="dialog"]', 'Rename fields').within(() => {
      cy.get(`div:has(> label:contains("${labelName}"))`)
        .find('input[type="text"]')
        .should('have.value', expectedValue);
    });
  },

  clickExportPopupButton: (buttonName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  },

  verifyDialogAppears: (name: string): void => {
    cy.contains('[role="dialog"]', name).should('be.visible');
  },

  nameTemplate: (templateName: string): void => {
    cy.contains('[role="dialog"]', 'Name Template').within(() => {
      cy.get('#export-template-name').clear();
      cy.get('#export-template-name').type(templateName);
    });
  },

  clickNewTemplateDialogButton: (buttonName: string): void => {
    cy.contains('[role="dialog"]', 'Name Template')
      .contains('button', buttonName)
      .click();
  },

  verifySuccessMessage: (message: string): void => {
    cy.get('[role="alert"]').should('be.visible').and('contain', message);
  },

  selectExportTemplate: (templateName: string): void => {
    cy.get('[data-test="panel-bulk-export"]').within(() => {
      cy.get('#export-template-selected').click();
    });
    cy.get('[role="option"]').contains(templateName).click();
  },

  navigateToTab: (tabName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .find('button[role="tab"]')
      .contains(tabName)
      .click();
  },

  clearArchiveNameField: (): void => {
    cy.get('[data-testid="export-file-name"]').find('input').clear();
  },

  verifyArchiveNameErrorMessage: (errorMessage: string): void => {
    cy.get('#export-file-name-helper-text')
      .should('be.visible')
      .and('contain', errorMessage);
  },

  verifyExportButtonDisabled: (): void => {
    cy.get('[data-test="export-select-button"]').should('be.disabled');
  },

  setArchiveName: (archiveName: string): void => {
    cy.get('[data-testid="export-file-name"]').find('input').type(archiveName);
  },

  verifyArchiveNameNoErrorMessage: (): void => {
    cy.get('#export-file-name-helper-text').should(
      'not.have.class',
      'Mui-error'
    );
  },

  verifyExportButtonEnabled: (): void => {
    cy.get('[data-test="export-select-button"]').should('be.enabled');
  },

  clickOptionTabButton: (buttonName: string): void => {
    cy.get('[data-test="panel-bulk-export"]')
      .contains('button', buttonName)
      .click();
  },

  verifySetPasswordDialogAppears: (): void => {
    cy.get('div:has(#encryption-password)').should('be.visible');
  },

  enterPassword: (password: string): void => {
    cy.get('#encryption-password').type(password);
  },

  clickPasswordDialogButton: (buttonName: string): void => {
    cy.get('div:has(#encryption-password)')
      .contains('button', buttonName)
      .click();
  },

  verifySetPasswordDialogDisappears: (): void => {
    cy.get('#encryption-password').should('not.exist');
  },

  openNotificationsPanel: (): void => {
    cy.get('#notification-image').click();
  },

  downloadFirstCompletedExportFromNotifications: (): void => {
    cy.contains('li', 'complete', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .click();
  },

  closeNotificationsPanel: (): void => {
    cy.get('body').click(0, 0);
  },

  navigateToMainApplicationTab: (tabName: string): void => {
    cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
  },

  verifyCanDownloadFirstCompletedExport: (): void => {
    cy.get('tbody tr:has(span:contains("complete"))', { timeout: 120000 })
      .first()
      .find('button:has([data-testid="SaveAltIcon"])')
      .should('be.enabled');
  },

  navigateToHistoryTab: (tabName: string): void => {
    cy.get(`[data-test="history-tab-button"]`).contains(tabName).click();
  },

  verifyTabDisplaysTotalFiles: (tabName: string): void => {
    cy.get('[data-test="history-tab-button"]')
      .contains(new RegExp(`^${tabName}\\(\\d+\\)$`))
      .should('be.visible');
  },

  verifyExportsTableColumns: (columns: string[]): void => {
    columns.forEach((columnName: string) => {
      cy.get('[data-testid="export-table-row"]')
        .closest('table')
        .find('thead th')
        .contains(columnName)
        .should('be.visible');
    });
  },

  verifyTableHasPaginationControls: (): void => {
    cy.get('div[role="combobox"]').should('be.visible');
    cy.contains(/\d+–\d+ of \d+/).should('be.visible');
    cy.get('button[aria-label="Previous Page"]').should('be.visible');
    cy.get('button[aria-label="Next Page"]').should('be.visible');
  },

  verifyExportFileNamesFollowDefaultFormat: (): void => {
    const nameFormatRegex = /^export_\d{8}_\d{2}_\d{2}_\d{2}$/;
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row)
        .find('td')
        .eq(1)
        .invoke('text')
        .should('match', nameFormatRegex);
    });
  },

  verifyExportFilesHaveStatus: (expectedStatus: string): void => {
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row).find('td').eq(2).should('have.text', expectedStatus);
    });
  },

  verifyDateInitiatedFormat: (): void => {
    const dateFormatRegex = /^\w{3}\s\w{3}\s\d{1,2}\s\d{4}\s\d{2}:\d{2}:\d{2}$/;
    cy.get('[data-testid="export-table-row"]').each(($row) => {
      cy.wrap($row)
        .find('td')
        .eq(3)
        .invoke('text')
        .should('match', dateFormatRegex);
    });
  },

  clickDownloadButtonForExportFile: (): void => {
    cy.get('[data-testid="export-table-row"]')
      .first()
      .find('button[data-name]')
      .click();
  },

  verifyRowsPerPageDefault: (defaultRows: string): void => {
    cy.get('div[role="combobox"]').should('have.text', defaultRows);
  },

  verifyChangingRowsPerPage: (rowsPerPageValues: string[]): void => {
    cy.contains(/\d+–\d+ of \d+/)
      .invoke('text')
      .then((initialText) => {
        const totalResults = parseInt(initialText.split(' of ')[1], 10);

        rowsPerPageValues.forEach((rowsPerPage: string) => {
          const expectedRowCount = parseInt(rowsPerPage, 10);
          const expectedVisibleRows = Math.min(expectedRowCount, totalResults);

          cy.get('div[role="combobox"]').click();
          cy.get(`li[role="option"][data-value="${rowsPerPage}"]`).click();

          const expectedText = `1–${expectedVisibleRows} of ${totalResults}`;
          cy.contains(expectedText).should('be.visible');
        });
        return null;
      });
  },

  verifyPaginationControlsForPage: (page: string): void => {
    if (page === 'first') {
      cy.contains(/1–\d+ of \d+/).should('be.visible');
      cy.get('button[aria-label="Previous Page"]').should('be.disabled');
      cy.get('button[aria-label="Next Page"]').should('be.enabled');
    } else if (page === 'second') {
      cy.contains(/11–\d+ of \d+/).should('be.visible');
      cy.get('button[aria-label="Previous Page"]').should('be.enabled');
      cy.get('button[aria-label="Next Page"]').should('be.enabled');
    }
  },

  clickPageButton: (direction: string): void => {
    cy.get(`button[aria-label="${direction} Page"]`).click();
  },
};
